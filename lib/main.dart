import 'dart:io';

import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:get_storage/get_storage.dart';
import 'package:quycky/core/services/push_service/push_service_fcm_implementation.dart';
import 'package:quycky/core/services/push_service/notification_test_helper.dart';
import 'package:quycky/core/services/system_badge_service/system_badge_service.dart';
import 'package:quycky/core/services/app_lifecycle_service/app_lifecycle_service.dart';
import 'package:quycky/core/services/notification_service/local_notifications_service.dart';
import 'package:quycky/core/utils/app_env.dart';
import 'package:quycky/firebase_options.dart';

import 'app/modules/app_module.dart';
import 'app/presenter/app_widget.dart';
import './analytics.dart';

class MyHttpOverrides extends HttpOverrides {
  @override
  HttpClient createHttpClient(SecurityContext? context) {
    return super.createHttpClient(context)
      ..badCertificateCallback =
          (X509Certificate cert, String host, int port) => true;
  }
}
// static final facebookAppEvents = FacebookAppEvents();

@pragma('vm:entry-point')
Future<void> firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  // Inicializa os serviços necessários para o isolate de background.
  await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);

  // Log detalhado da mensagem recebida
  NotificationTestHelper.logIncomingMessage(message, 'BACKGROUND');

  try {
    // Atualiza o badge do ícone do app com a nova contagem.
    // Apenas um handler de background para evitar badges duplicados
    await SystemBadgeService.updateBadgeCount(1);
    debugPrint('🔔 Badge updated successfully');
  } catch (e) {
    debugPrint('🔔 Error updating badge: ${e.toString()}');
  }

  // Mantém a lógica existente para quando o app for aberto pela notificação.
  FCMBackgroundMessageHandle.remoteMessage = message;

  debugPrint('🔔 Background handler completed');
}

void main() async {
  try {
    WidgetsFlutterBinding.ensureInitialized();
    FirebaseMessaging.onBackgroundMessage(firebaseMessagingBackgroundHandler);
    await AppEnv.initialize();
    await Firebase.initializeApp(
        options: DefaultFirebaseOptions.currentPlatform);
    await GetStorage.init();

    // Inicializa o serviço de ciclo de vida do app
    AppLifecycleService().initialize();

    // Inicializa o serviço de notificações locais
    await LocalNotificationService.initialize();

    // await Firebase.initializeApp(
    //   options: DefaultFirebaseOptions.currentPlatform,
    // );
    Analytics.initialize();
  } catch (e) {
    debugPrint("err->: $e");
  }
  runApp(ModularApp(module: AppModule(), child: const AppWidget())
      // ChangeNotifierProvider<ThemeStore>(create: (context) => ThemeStore(),
      //     child: ModularApp(module: AppModule(), child: AppWidget())),
      );
  // HttpOverrides.global = MyHttpOverrides();
}
