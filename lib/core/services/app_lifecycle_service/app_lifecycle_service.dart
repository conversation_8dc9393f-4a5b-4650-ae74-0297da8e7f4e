import 'package:flutter/material.dart';

/// Serviço para monitorar o estado do ciclo de vida do app
class AppLifecycleService with WidgetsBindingObserver {
  static final AppLifecycleService _instance = AppLifecycleService._internal();
  factory AppLifecycleService() => _instance;
  AppLifecycleService._internal();

  AppLifecycleState _currentState = AppLifecycleState.resumed;
  bool _isInitialized = false;

  /// Estado atual do app
  AppLifecycleState get currentState => _currentState;

  /// Verifica se o app está em background
  bool get isInBackground => 
      _currentState == AppLifecycleState.paused || 
      _currentState == AppLifecycleState.hidden ||
      _currentState == AppLifecycleState.detached;

  /// Verifica se o app está em foreground
  bool get isInForeground => _currentState == AppLifecycleState.resumed;

  /// Inicializa o serviço
  void initialize() {
    if (_isInitialized) return;
    
    WidgetsBinding.instance.addObserver(this);
    _isInitialized = true;
    debugPrint('AppLifecycleService: Inicializado');
  }

  /// Finaliza o serviço
  void dispose() {
    if (!_isInitialized) return;
    
    WidgetsBinding.instance.removeObserver(this);
    _isInitialized = false;
    debugPrint('AppLifecycleService: Finalizado');
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    
    final previousState = _currentState;
    _currentState = state;
    
    debugPrint('AppLifecycleService: Estado mudou de $previousState para $state');
    
    switch (state) {
      case AppLifecycleState.resumed:
        debugPrint('AppLifecycleService: App em foreground');
        break;
      case AppLifecycleState.paused:
        debugPrint('AppLifecycleService: App em background');
        break;
      case AppLifecycleState.hidden:
        debugPrint('AppLifecycleService: App oculto');
        break;
      case AppLifecycleState.detached:
        debugPrint('AppLifecycleService: App desanexado');
        break;
      case AppLifecycleState.inactive:
        debugPrint('AppLifecycleService: App inativo');
        break;
    }
  }
}
