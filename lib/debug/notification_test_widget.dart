import 'package:flutter/material.dart';
import 'package:quycky/core/services/notification_service/local_notifications_service.dart';
import 'package:quycky/core/services/app_lifecycle_service/app_lifecycle_service.dart';

/// Widget para testar notificações - apenas para debug
class NotificationTestWidget extends StatelessWidget {
  const NotificationTestWidget({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Positioned(
      bottom: 100,
      right: 20,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          FloatingActionButton(
            heroTag: "test_notification",
            onPressed: () async {
              debugPrint('🧪 Testando notificação...');
              await LocalNotificationService.showTestNotification();
            },
            backgroundColor: Colors.orange,
            child: const Icon(Icons.notifications_active),
          ),
          const SizedBox(height: 10),
          FloatingActionButton(
            heroTag: "check_status",
            onPressed: () {
              final appLifecycle = AppLifecycleService();
              final isNotificationInitialized = LocalNotificationService.isInitialized;
              
              debugPrint('🧪 Status do sistema:');
              debugPrint('🧪 App State: ${appLifecycle.currentState}');
              debugPrint('🧪 Is Background: ${appLifecycle.isInBackground}');
              debugPrint('🧪 Is Foreground: ${appLifecycle.isInForeground}');
              debugPrint('🧪 Notification Service Initialized: $isNotificationInitialized');
              
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(
                    'App: ${appLifecycle.currentState.name}\n'
                    'Background: ${appLifecycle.isInBackground}\n'
                    'Notifications: $isNotificationInitialized',
                  ),
                  duration: const Duration(seconds: 3),
                ),
              );
            },
            backgroundColor: Colors.blue,
            child: const Icon(Icons.info),
          ),
        ],
      ),
    );
  }
}
